@extends('layouts.main')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-secondary via-white to-accent/20">
    <!-- Hero Section -->
    <div class="relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-primary-500 to-primary-600 opacity-90"></div>
        <div class="absolute inset-0">
            <svg class="absolute bottom-0 left-0 w-full h-24 text-white fill-current" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25"></path>
                <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5"></path>
                <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"></path>
            </svg>
        </div>

        <div class="relative container mx-auto px-4 py-20">
            <div class="text-center text-white" data-aos="fade-up">
                <h1 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                    Temukan Event
                    <span class="block bg-gradient-to-r from-accent to-yellow-300 bg-clip-text text-transparent">
                        Terbaik
                    </span>
                </h1>
                <p class="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto">
                    Jelajahi berbagai event menarik dan dapatkan tiketmu sekarang!
                    Dari konser musik hingga seminar bisnis, semua ada di TikPro.
                </p>

                <!-- Search Bar -->
                <div class="max-w-2xl mx-auto relative" data-aos="fade-up" data-aos-delay="200">
                    <div class="relative">
                        <input type="text"
                               id="heroSearch"
                               class="w-full px-6 py-5 pl-14 pr-32 rounded-2xl bg-white/95 backdrop-blur-sm shadow-2xl focus:outline-none focus:ring-4 focus:ring-white/30 text-gray-800 text-lg placeholder-gray-500"
                               placeholder="Cari event, konser, atau seminar...">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                        <button class="absolute right-2 top-1/2 -translate-y-1/2 bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-3 rounded-xl hover:shadow-lg transition-all duration-300 font-semibold">
                            Cari
                        </button>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="400">
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white">1000+</div>
                        <div class="text-white/80 text-sm md:text-base">Event Tersedia</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white">50K+</div>
                        <div class="text-white/80 text-sm md:text-base">Tiket Terjual</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white">25+</div>
                        <div class="text-white/80 text-sm md:text-base">Kota</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white">4.9</div>
                        <div class="text-white/80 text-sm md:text-base">Rating</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Filters -->
    <div class="flex overflow-x-auto space-x-4 mb-8 pb-4 scrollbar-hide" data-aos="fade-up" data-aos-delay="100">
        <button class="px-6 py-3 rounded-full bg-primary text-white shadow-md flex-shrink-0 hover:bg-primary/90 transition">
            Semua
        </button>
        <button class="px-6 py-3 rounded-full bg-white text-gray-700 shadow-md flex-shrink-0 hover:bg-gray-50 transition">
            Konser
        </button>
        <button class="px-6 py-3 rounded-full bg-white text-gray-700 shadow-md flex-shrink-0 hover:bg-gray-50 transition">
            Festival
        </button>
        <button class="px-6 py-3 rounded-full bg-white text-gray-700 shadow-md flex-shrink-0 hover:bg-gray-50 transition">
            Seminar
        </button>
        <button class="px-6 py-3 rounded-full bg-white text-gray-700 shadow-md flex-shrink-0 hover:bg-gray-50 transition">
            Cinema
        </button>
        <button class="px-6 py-3 rounded-full bg-white text-gray-700 shadow-md flex-shrink-0 hover:bg-gray-50 transition">
            Olahraga
        </button>
        <button class="px-6 py-3 rounded-full bg-white text-gray-700 shadow-md flex-shrink-0 hover:bg-gray-50 transition">
            Workshop
        </button>
    </div>

    <!-- Events Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" data-aos="fade-up" data-aos-delay="200">
        @forelse($events as $event)
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
            <img src="{{ $event->poster_url }}" alt="{{ $event->title }}" class="w-full h-48 object-cover">
            <div class="p-6">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="px-3 py-1 bg-accent/10 text-accent rounded-full text-sm">{{ $event->category }}</span>
                    <span class="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">{{ $event->formatted_date }}</span>
                </div>
                <h3 class="text-xl font-bold mb-2">{{ $event->title }}</h3>
                <p class="text-gray-600 mb-4">{{ $event->location }}</p>
                <div class="flex justify-between items-center">
                    <span class="text-lg font-bold text-primary">Rp {{ number_format($event->price, 0, ',', '.') }}</span>
                    <a href="{{ route('events.show', $event) }}"
                       class="px-6 py-2 bg-primary text-white rounded-full hover:bg-primary/90 transition">
                        Detail
                    </a>
                </div>
            </div>
        </div>
        @empty
        <div class="col-span-full text-center py-12">
            <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M12 12h.01M12 12h.01M12 12h.01M12 12h.01M12 12h.01"/>
            </svg>
            <p class="text-gray-500 text-lg">Belum ada event yang tersedia</p>
        </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($events->hasPages())
    <div class="mt-8" data-aos="fade-up" data-aos-delay="300">
        {{ $events->links() }}
    </div>
    @endif
</div>

<!-- Install PWA Prompt -->
<div id="installPrompt" class="hidden fixed bottom-20 left-4 right-4 md:left-auto md:right-4 md:bottom-4 md:w-96 bg-white rounded-2xl shadow-2xl p-6">
    <h4 class="text-lg font-bold mb-2">Install TikPro App</h4>
    <p class="text-gray-600 mb-4">Install aplikasi TikPro untuk pengalaman yang lebih baik dan akses tiket offline.</p>
    <div class="flex justify-end space-x-4">
        <button onclick="this.parentElement.parentElement.classList.add('hidden')" class="text-gray-500 hover:text-gray-700">
            Nanti
        </button>
        <button id="installButton" class="px-6 py-2 bg-primary text-white rounded-full hover:bg-primary/90 transition">
            Install
        </button>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load featured events
    loadFeaturedEvents();

    // Setup search functionality
    setupSearch();

    // Setup PWA install prompt
    setupPWAInstall();
});

async function loadFeaturedEvents() {
    try {
        const response = await fetch('/tickets/featured');
        const events = await response.json();

        const container = document.querySelector('#featured-events') ||
                         document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3');

        if (container) {
            if (events.length > 0) {
                container.innerHTML = events.map((event, index) => createEventCard(event, index * 100)).join('');
                // Reinitialize AOS for new elements
                AOS.refresh();
            } else {
                container.innerHTML = `
                    <div class="col-span-full text-center py-12">
                        <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M12 12h.01"/>
                        </svg>
                        <p class="text-gray-500 text-lg">Belum ada event yang tersedia</p>
                    </div>
                `;
            }
        }
    } catch (error) {
        console.error('Error loading featured events:', error);
        const container = document.querySelector('#featured-events') ||
                         document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3');
        if (container) {
            container.innerHTML = `
                <div class="col-span-full text-center py-12">
                    <p class="text-red-500">Gagal memuat event. Silakan refresh halaman.</p>
                </div>
            `;
        }
    }
}

function createEventCard(event, delay = 0) {
    const statusBadge = getStatusBadge(event);

    return `
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow card-hover"
             data-aos="fade-up" data-aos-delay="${delay}">
            <div class="relative">
                <img src="${event.poster}" alt="${event.title}" class="w-full h-48 object-cover">
                ${statusBadge}
                <div class="absolute top-4 right-4">
                    <button onclick="toggleWishlist(${event.id})"
                            class="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors duration-200 wishlist-btn"
                            data-event-id="${event.id}">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">${event.category}</span>
                    <span class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">${event.start_date}</span>
                </div>
                <h3 class="text-xl font-bold mb-2 line-clamp-2">${event.title}</h3>
                <p class="text-gray-600 mb-4 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                    ${event.venue_name}, ${event.city}
                </p>
                <div class="flex justify-between items-center">
                    <span class="text-lg font-bold text-primary">${event.price}</span>
                    <a href="${event.url}"
                       class="px-6 py-2 bg-gradient-to-r from-primary to-secondary text-white rounded-lg hover:shadow-lg transition-all duration-300 font-semibold">
                        Detail
                    </a>
                </div>
            </div>
        </div>
    `;
}

function getStatusBadge(event) {
    if (event.is_free) {
        return `
            <div class="absolute top-4 left-4">
                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Gratis
                </span>
            </div>
        `;
    }

    if (event.available_tickets <= 10) {
        return `
            <div class="absolute top-4 left-4">
                <span class="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Terbatas
                </span>
            </div>
        `;
    }

    return '';
}

function setupSearch() {
    const searchInput = document.querySelector('input[placeholder*="Cari event"]');
    const searchButton = searchInput?.nextElementSibling;

    if (searchButton) {
        searchButton.addEventListener('click', function() {
            performSearch();
        });
    }

    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }
}

function performSearch() {
    const searchInput = document.querySelector('input[placeholder*="Cari event"]');
    const query = searchInput?.value.trim();

    if (query) {
        window.location.href = `/ticket/events?search=${encodeURIComponent(query)}`;
    }
}

async function toggleWishlist(eventId) {
    if (!@json(auth()->check())) {
        window.showNotification('Silakan login terlebih dahulu', 'warning');
        return;
    }

    const button = document.querySelector(`[data-event-id="${eventId}"]`);
    const icon = button?.querySelector('svg');

    try {
        const response = await fetch(`/ticket/events/${eventId}/wishlist`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();

        if (response.ok) {
            icon?.classList.toggle('text-red-500');
            icon?.classList.toggle('fill-current');
            window.showNotification(data.message, 'success');
        } else {
            window.showNotification(data.error || 'Terjadi kesalahan', 'error');
        }
    } catch (error) {
        window.showNotification('Terjadi kesalahan jaringan', 'error');
    }
}

function setupPWAInstall() {
    let deferredPrompt;
    const installPrompt = document.getElementById('installPrompt');
    const installButton = document.getElementById('installButton');

    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;

        // Show install prompt after 5 seconds
        setTimeout(() => {
            if (installPrompt) {
                installPrompt.classList.remove('hidden');
            }
        }, 5000);
    });

    if (installButton) {
        installButton.addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;

                if (outcome === 'accepted') {
                    window.showNotification('Aplikasi berhasil diinstall!', 'success');
                }

                deferredPrompt = null;
                installPrompt?.classList.add('hidden');
            }
        });
    }
}
</script>
@endpush